import "@fontsource/caveat/500.css";

export default function SignatureMethod({ signatureMethod, signature, setSignature }) {
  return (
    <div className="w-full">
      <label htmlFor="signature" className="block text-sm font-medium text-gray-700 mb-1">
        {signatureMethod === "type" ? "Type Your Signature" : "Draw Your Signature"}
      </label>
      {signatureMethod === "type" ? (
        <input
          id="signature"
          name="signature"
          type="text"
          placeholder="Type your full name"
          value={signature}
          onChange={(e) => setSignature(e.target.value)}
          className="w-full font-caveat text-4xl p-2 py-7 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
          required
        />
      ) : (
        <div className="w-full h-24 border border-gray-300 rounded text-sm bg-gray-50 flex items-center justify-center text-gray-500">
          Signature canvas will be implemented here
        </div>
      )}
    </div>
  );
}
